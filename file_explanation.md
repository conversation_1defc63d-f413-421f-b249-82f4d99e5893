# File Explanation - Pydantic AI MCP Project

This document explains the purpose and usage of each file in the Pydantic AI MCP (Model Control Protocol) project.

## Project Overview

This project demonstrates the integration of Pydantic AI with MCP servers, specifically using Azure OpenAI API and Context7 documentation server. The project showcases various approaches to building AI agents with external tool capabilities.

## Core Files

### 1. `main.py` - Basic MCP Integration Example
**Purpose**: Demonstrates basic integration of Pydantic AI with MCP servers using Azure OpenAI.

**Key Features**:
- Azure OpenAI client setup with API key authentication
- Integration with Context7 MCP server via Deno
- HTTP MCP server connection (localhost:3001)
- Logfire instrumentation for monitoring
- Basic query execution with MCP tool usage

**How to Use**:
```bash
# Activate virtual environment
.venv\Scripts\activate

# Install dependencies
uv pip install pydantic-ai-slim[mcp,openai] logfire

# Run the example
python main.py
```

**Dependencies**: Azure OpenAI endpoint, Deno runtime for Context7

---

### 2. `test_main_context7.py` - Context7 Focused Testing
**Purpose**: Focused testing of Context7 MCP server integration with enhanced error handling and logging.

**Key Features**:
- Simplified Context7-only MCP setup
- Enhanced error handling with timeouts
- File-based output for model responses
- Comprehensive logging with Python's logging module
- Explicit Deno path configuration for Windows

**How to Use**:
```bash
# Ensure Deno is installed and accessible
deno --version

# Run the test
python test_main_context7.py

# Check output in model_response.txt
```

**Output**: Creates `model_response.txt` with the AI agent's response

---

### 3. `fastmcp_tool_example.py` - Advanced Tool Integration
**Purpose**: Comprehensive example showing custom tool creation and MCP server integration.

**Key Features**:
- Custom tool definitions (time, factorial, weather)
- Structured output using Pydantic models
- Weather API integration (OpenWeatherMap)
- Context7 server with fallback handling
- Multiple query processing examples
- Logfire instrumentation

**Custom Tools**:
- `get_current_time()`: Returns current date/time
- `calculate_factorial(n)`: Calculates factorial of number
- `get_city_weather(city, data_type)`: Fetches weather data

**How to Use**:
```bash
# Install additional dependencies
uv pip install requests

# Get OpenWeatherMap API key (optional for weather tool)
# Set OPENWEATHER_API_KEY environment variable

# Run the example
python fastmcp_tool_example.py
```

---

### 4. `assistant_agent.py` - A2A Agent Server
**Purpose**: Implements an Agent-to-Agent (A2A) communication server using the python-a2a framework.

**Key Features**:
- HTTP server for A2A communication
- Function call handling (time, calculations)
- Conversation context management
- Message threading and parent-child relationships
- Error handling for function calls

**Available Functions**:
- `get_current_time`: Returns current timestamp
- `calculate`: Basic arithmetic operations (add, subtract, multiply, divide)

**How to Use**:
```bash
# Install A2A dependency
uv pip install python-a2a

# Run the server
python assistant_agent.py

# Server runs on http://localhost:5000
```

---

### 5. `client_example.py` - A2A Client Demo
**Purpose**: Demonstrates how to interact with the A2A agent server as a client.

**Key Features**:
- Text message interaction
- Function call demonstrations
- Error handling examples
- Conversation management
- Multi-turn dialogue examples

**How to Use**:
```bash
# Ensure assistant_agent.py is running first
python assistant_agent.py

# In another terminal, run the client
python client_example.py
```

---

### 6. `sample_fastmcp.py` - Minimal FastMCP Example
**Purpose**: Minimal example showing FastMCP server setup.

**Content**: Basic FastMCP server configuration template.

---

## Configuration Files

### 7. `pyproject.toml` - Project Configuration
**Purpose**: Python project metadata and build configuration.

**Key Settings**:
- Project name: `pydanticmcp`
- Python version requirement: `>=3.9`
- Build system: setuptools
- License: MIT

### 8. `requirements.txt` - Dependencies
**Purpose**: Lists project dependencies for pip/uv installation.

**Dependencies**:
- `pydantic-ai-slim[mcp,openai]`: Core AI framework with MCP support
- `logfire[fastapi]`: Monitoring and instrumentation
- `python-a2a>=0.1.0`: Agent-to-Agent communication framework

### 9. `.windsurfrules` - IDE Configuration
**Purpose**: Configuration for Windsurf IDE behavior and preferences.

---

## Utility Files

### 10. `start_mcp_server.bat` - Windows Batch Script
**Purpose**: Windows batch file to start MCP servers easily.

**Usage**: Double-click to run or execute from command line.

### 11. `verify_conversation.py` - Conversation Testing
**Purpose**: Tests conversation handling and message linking functionality.

### 12. `test_message_linking.py` - Message Threading Tests
**Purpose**: Tests message parent-child relationships and conversation threading.

### 13. `test_deno.py` - Deno Integration Test
**Purpose**: Tests Deno runtime integration for Context7 MCP server.

---

## Output Files

### 14. `model_response.txt` - AI Response Output
**Purpose**: Contains the latest response from AI model (generated by test scripts).

**Content**: Text output from AI agent queries, useful for debugging and analysis.

---

## Setup Instructions

### Prerequisites
1. **Python 3.13** (as specified in user rules)
2. **UV package manager** (for dependency management)
3. **Deno runtime** (for Context7 MCP server)
4. **Azure OpenAI API access** (endpoint and API key)

### Installation Steps
```bash
# 1. Create and activate virtual environment
python -m venv .venv
.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac

# 2. Install dependencies using UV
uv pip install -r requirements.txt

# 3. Install Deno (for Context7)
# Windows: winget install DenoLand.Deno
# Or download from https://deno.land/

# 4. Configure Azure OpenAI credentials
# Update API key and endpoint in the Python files

# 5. Run examples
python main.py
python test_main_context7.py
python fastmcp_tool_example.py
```

### Environment Variables (Optional)
- `OPENWEATHER_API_KEY`: For weather tool functionality
- `AZURE_OPENAI_ENDPOINT`: Azure OpenAI endpoint URL
- `AZURE_OPENAI_API_KEY`: Azure OpenAI API key

---

## Usage Patterns

### Basic MCP Integration
Use `main.py` or `test_main_context7.py` for simple MCP server integration with Context7.

### Custom Tool Development
Use `fastmcp_tool_example.py` as a template for creating custom tools and structured outputs.

### Agent-to-Agent Communication
Use `assistant_agent.py` and `client_example.py` for A2A communication patterns.

### Testing and Development
Use the various test files (`test_*.py`) for specific functionality testing.

---

## Troubleshooting

### Common Issues
1. **Deno not found**: Ensure Deno is installed and in PATH
2. **Azure OpenAI connection**: Verify endpoint URL and API key
3. **MCP server timeout**: Check network connectivity and server availability
4. **Import errors**: Ensure all dependencies are installed in the virtual environment

### Debug Tips
- Check `model_response.txt` for AI agent outputs
- Use Logfire dashboard for monitoring (when configured)
- Enable verbose logging in test scripts
- Verify MCP server connectivity before running agents

---

## Next Steps

1. **Customize Tools**: Modify `fastmcp_tool_example.py` to add your own tools
2. **Extend A2A**: Add more functions to `assistant_agent.py`
3. **Production Setup**: Configure proper authentication and error handling
4. **Monitoring**: Set up Logfire for production monitoring
5. **Testing**: Add unit tests for custom tools and agents

This project serves as a comprehensive foundation for building AI agents with MCP server integration using Pydantic AI framework.
