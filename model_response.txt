Here is a complete code example to create and run an MCP server in Python using the fastmcp library. This snippet sets up a basic server, defines a simple tool, and runs the server using STDIO transport (the default).

Make sure you have fastmcp installed (pip install fastmcp).

Example: Basic fastmcp server

from fastmcp import FastMCP

# Create the MCP server instance
mcp = FastMCP("Demo 🚀")

# Define a tool using @mcp.tool() decorator
@mcp.tool()
def hello(name: str) -> str:
    return f"Hello, {name}!"

if __name__ == "__main__":
    mcp.run()  # By default, uses STDIO transport

How to run:

python server.py

- This starts your MCP server, exposing the hello tool.
- You can connect to this server from fastmcp-compatible clients.
To add or test more tools, simply define additional functions with the @mcp.tool() decorator before mcp.run().